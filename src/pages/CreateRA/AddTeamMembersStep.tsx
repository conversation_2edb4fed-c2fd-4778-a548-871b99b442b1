import React, {forwardRef, useEffect, useImperativeHandle} from 'react';
import {TemplateForm} from '../../types/template';
import {RiskForm} from '../../types/risk';
import {format} from 'date-fns';
import {Card} from 'react-bootstrap';
import {CrewIcon, CrossIcon} from '../../utils/svgIcons';
import {getCrewList} from '../../services/services';
import SearchCrewMember from '../../components/SearchCrewMember';
import {UsernameProfile} from '../../components/UsernameProfile';
import {CrewMember} from '../../types';
import {useDataStoreContext} from '../../context';

interface AddTeamMembersStepRefProps {
  form: TemplateForm | RiskForm;
  setForm: React.Dispatch<React.SetStateAction<TemplateForm | RiskForm>>;
  onValidate: (isValid: boolean) => void;
}

export interface AddTeamMembersStepRef {
  validate: () => boolean;
}

export const AddTeamMembersStep = forwardRef<
  AddTeamMembersStepRef,
  AddTeamMembersStepRefProps
>(({form, setForm, onValidate}, ref) => {
  // const {
  //   dataStore: {crewMembersListForRisk},
  // } = useDataStoreContext();
  const [crewList, setCrewList] = React.useState<CrewMember[]>([]);

  useEffect(() => {
    const fetchCrewList = async () => {
      try {
        if ('vessel_id' in form) {
          const vesselId = form.vessel_id;
          if (vesselId) {
            const resp = await getCrewList(vesselId);
            setCrewList(resp);
            // setCrewList(crewMembersListForRisk);
          }
        }
      } catch (error) {
        console.error('Error fetching crew members:', error);
      }
    };
    fetchCrewList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [(form as RiskForm).vessel_id]);

  const validate = (): boolean => {
    const valid = (form as RiskForm).risk_team_member?.length > 0;
    onValidate(valid);
    return valid;
  };

  useImperativeHandle(ref, () => ({
    validate,
  }));

  useEffect(() => {
    if ('risk_team_member' in form) {
      validate();
    }
  }, [form]);

  const handleTeamMemberSelection = (selectedIds: string[]) => {
    if (selectedIds.length === 0 || !('risk_team_member' in form)) return;

    const selectedId = selectedIds[0]; // SearchCrewMember returns array with single ID
    const selectedCrewMember = crewList.find(
      crew => crew.seafarer_id.toString() === selectedId,
    );

    if (!selectedCrewMember) return;

    // Check if team member already exists to prevent duplicates
    const existingTeamMember = (form as RiskForm).risk_team_member?.find(
      member => member.seafarer_id === selectedCrewMember.seafarer_id,
    );

    if (existingTeamMember) {
      // Team member already exists, don't add duplicate
      return;
    }

    // Convert CrewMember to RiskTeamMember format
    const newTeamMember = {
      seafarer_id: selectedCrewMember.seafarer_id,
      seafarer_person_id: selectedCrewMember.seafarer_person_id,
      seafarer_hkid: selectedCrewMember.seafarer_hkid,
      seafarer_rank_id: selectedCrewMember.seafarer_rank_id,
      seafarer_name: selectedCrewMember.seafarer_name,
      seafarer_rank: selectedCrewMember.seafarer_rank,
      seafarer_rank_sort_order: selectedCrewMember.seafarer_rank_sort_order,
    };

    // Add new team member to the form
    setForm(prevForm => ({
      ...prevForm,
      risk_team_member: [
        ...(prevForm as RiskForm).risk_team_member,
        newTeamMember,
      ],
    }));
  };

  return (
    <>
      <div className="d-flex justify-content-between align-items-center mb-3">
        <div>
          <div className="secondary-color fs-20 fw-600">
            {form?.task_requiring_ra || ''}
          </div>
          {'date_risk_assessment' in form && form.date_risk_assessment && (
            <div className="text-muted fs-14">
              Date of Risk Assessment:{' '}
              {format(new Date(form.date_risk_assessment), 'dd MMM yyyy')}
            </div>
          )}
        </div>
      </div>
      <hr style={{marginRight: '-1rem', marginLeft: '-1rem'}} />
      <div className="secondary-color fs-20 fw-600 mb-4">Add Team Members</div>
      <div className="mb-4 search-crew-bar">
        <SearchCrewMember
          value={[]}
          options={crewList.map(user => ({
            id: user.seafarer_id.toString(),
            full_name: String(user.seafarer_name),
            subText: `${user.seafarer_rank} •HK ID: ${user.seafarer_hkid}`,
          }))}
          placeholder="Search Name, Rank or Email ID"
          onChange={handleTeamMemberSelection}
        />
      </div>
      <Card className="h-60vh">
        <Card.Body
          className={`${
            (form as RiskForm).risk_team_member?.length === 0
              ? 'd-flex justify-content-center align-items-center'
              : 'p-3'
          }`}
        >
          {(form as RiskForm).risk_team_member?.length === 0 ? (
            <div className="d-flex flex-column align-items-center gap-16px">
              <CrewIcon />
              <div className="fs-14 text-muted text-center">
                Search and Add the Team Members <br />
                involved in preparing the Risk Assessment
              </div>
            </div>
          ) : (
            <div>
              <div className="d-flex flex-wrap gap-24p">
                {(form as RiskForm).risk_team_member?.map(member => (
                  <div
                    key={member.seafarer_id}
                    className="d-flex align-items-center justify-content-between border rounded crew-member-profile"
                  >
                    <div className="d-flex align-items-center">
                      <UsernameProfile
                        username={member.seafarer_name}
                        subText={`${member.seafarer_rank} • HK ID: ${member.seafarer_hkid}`}
                      />
                    </div>
                    <button
                      type="button"
                      className="btn"
                      onClick={() => {
                        setForm(prevForm => ({
                          ...prevForm,
                          risk_team_member:
                            (prevForm as RiskForm).risk_team_member?.filter(
                              m => m.seafarer_id !== member.seafarer_id,
                            ) || [],
                        }));
                      }}
                    >
                      <CrossIcon />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card.Body>
      </Card>
    </>
  );
});

AddTeamMembersStep.displayName = 'AddTeamMembersStep';
